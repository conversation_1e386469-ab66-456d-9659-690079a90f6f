{"openapi": "3.0.0", "info": {"title": "Time Information API", "version": "1.0.0", "description": "API to get current time information in IST timezone"}, "paths": {"/getCurrentTime": {"get": {"summary": "Get current time", "description": "Returns the current time in UTC and IST", "operationId": "getCurrentTime", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"utc_time": {"type": "string", "description": "Current time in UTC", "example": "2023-11-15 14:30:45 UTC"}, "ist_time": {"type": "string", "description": "Current time in IST (Indian Standard Time)", "example": "2023-11-15 20:00:45 IST"}, "timestamp": {"type": "integer", "description": "Current timestamp in seconds", "example": 1700057445}}}}}}}}}}}