{"name": "invoke_flow", "openapi": "3.0.0", "info": {"title": "Invoke Flow API", "version": "1.0.0", "description": "API to invoke a Bedrock flow"}, "paths": {"/getFlowResponse": {"post": {"summary": "Get bedrock flow resonse", "description": "Returns the bedrock flow response", "operationId": "getFlowResponse", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_input"], "properties": {"user_input": {"type": "string", "description": "Input from the user", "example": "Find me a hotel in Puducherry"}}}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"flow_response": {"type": "string", "description": "Response from the bedrock flow"}, "completion_reason": {"type": "string", "description": "Reason for the completion of the flow"}}}}}}}, "400": {"description": "Bad request. One or more required fields are missing or invalid."}, "500": {"description": "Server error"}}}}}