{"openapi": "3.0.0", "info": {"title": "Hotel API", "version": "1.0.0", "description": "API to search hotels and check room rates"}, "paths": {"/data/hotels": {"post": {"summary": "Search hotels by location", "description": "Returns available hotels for a specific location", "operationId": "searchHotels", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["city", "country_code"], "properties": {"city": {"type": "string", "description": "City name", "example": "Puducherry"}, "country_code": {"type": "string", "description": "Country code (ISO 2-letter)", "example": "IN"}, "latitude": {"type": "number", "format": "float", "description": "Latitude coordinate", "example": 11.932505672828558}, "longitude": {"type": "number", "format": "float", "description": "Longitude coordinate", "example": 79.81623698081638}, "limit": {"type": "integer", "description": "Maximum number of results to return", "default": 5, "example": 5}}}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"hotelId": {"type": "string", "description": "Unique identifier for the hotel"}, "name": {"type": "string", "description": "Name of the hotel"}, "rating": {"type": "number", "description": "Hotel rating"}, "address": {"type": "object", "description": "Hotel address details"}, "location": {"type": "object", "description": "Hotel location coordinates"}}}}}}}}}, "400": {"description": "Bad request. One or more required fields are missing or invalid."}, "500": {"description": "Server error"}}}}, "/hotels/rates": {"post": {"summary": "Get hotel room rates and availability", "description": "Returns available room types, rates, and booking options for specified hotels and dates", "operationId": "getHotelRates", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["hotelIds", "check_in", "check_out", "adults", "children", "currency", "guest_nationality", "cityName", "countryCode", "zip"], "properties": {"hotelIds": {"type": "array", "description": "List of hotel IDs to check availability for. Get the hotel IDs from the /data/hotels API.", "items": {"type": "string"}, "example": ["lp656bbe43"]}, "adults": {"type": "integer", "description": "Number of adults to check availability for", "example": 2}, "children": {"type": "integer", "description": "Number of children to check availability for", "example": 1}, "currency": {"type": "string", "description": "Currency code for pricing", "example": "INR"}, "guest_nationality": {"type": "string", "description": "Nationality of guest (ISO 2-letter code)", "example": "IN"}, "check_in": {"type": "string", "format": "date", "description": "Check-in date (YYYY-MM-DD)", "example": "2025-07-01"}, "check_out": {"type": "string", "format": "date", "description": "Check-out date (YYYY-MM-DD)", "example": "2025-07-02"}, "cityName": {"type": "string", "description": "City name", "example": "Puducherry"}, "countryCode": {"type": "string", "description": "Country code (ISO 2-letter)", "example": "IN"}, "zip": {"type": "string", "description": "ZIP/Postal code", "example": "605007"}}, "x-amzn-bedrock-parameterHints": {"hotelIds": {"type": "array"}}}, "example": {"hotelIds": ["lp656bbe43"], "adults": 2, "children": 1, "currency": "INR", "guest_nationality": "IN", "check_in": "2025-07-01", "check_out": "2025-07-02", "cityName": "Puducherry", "countryCode": "IN", "zip": "605007"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"hotelId": {"type": "string", "description": "Unique identifier for the hotel"}, "roomTypes": {"type": "array", "items": {"type": "object", "properties": {"roomTypeId": {"type": "string", "description": "Unique identifier for the room type"}, "offerId": {"type": "string", "description": "Unique identifier for the offer"}, "supplier": {"type": "string", "description": "Name of the supplier"}, "rates": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Room name"}, "maxOccupancy": {"type": "integer", "description": "Maximum number of guests"}, "adultCount": {"type": "integer", "description": "Number of adults"}, "childCount": {"type": "integer", "description": "Number of children"}, "boardType": {"type": "string", "description": "Board type code"}, "boardName": {"type": "string", "description": "Board type description"}, "retailRate": {"type": "object", "properties": {"total": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number", "description": "Total price amount"}, "currency": {"type": "string", "description": "Currency code"}}}}}}}}}, "offerRetailRate": {"type": "object", "properties": {"amount": {"type": "number", "description": "Retail rate amount"}, "currency": {"type": "string", "description": "Currency code"}}}}}}}}}}}}}}, "400": {"description": "Bad request. One or more required fields are missing or invalid."}, "500": {"description": "Server error"}}}}, "/data/reviews": {"post": {"summary": "Get hotel reviews", "description": "Returns reviews for a specific hotel", "operationId": "getHotelReviews", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["hotelId"], "properties": {"hotelId": {"type": "string", "description": "Hotel ID", "example": "lp655af390"}, "get_sentiment": {"type": "boolean", "description": "Whether to include sentiment analysis", "default": false, "example": false}}}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"hotelId": {"type": "string", "description": "Hotel ID"}, "reviews": {"type": "array", "items": {"type": "object", "properties": {"reviewId": {"type": "string", "description": "Review identifier"}, "title": {"type": "string", "description": "Review title"}, "text": {"type": "string", "description": "Review content"}, "rating": {"type": "number", "description": "Review rating"}, "date": {"type": "string", "description": "Review date"}, "sentiment": {"type": "object", "description": "Sentiment analysis results (if requested)"}}}}}}}}}}}, "400": {"description": "Bad request. One or more required fields are missing or invalid."}, "500": {"description": "Server error"}}}}}}