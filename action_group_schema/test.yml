openapi: 3.0.0
info:
  title: Hotel Availability API
  version: 1.0.0
  description: API to check hotel availability using LiteAPI
paths:
  /getHotelAvailability:
    post:
      summary: Get hotel availability
      description: Returns available hotels for a specific location
      operationId: getHotelAvailability
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                city:
                  type: string
                  description: City name
                country_code:
                  type: string
                  description: Country code (ISO 2-letter)
                latitude:
                  type: number
                  format: float
                  description: Latitude of the location
                longitude:
                  type: number
                  format: float
                  description: Longitude of the location
                limit:
                  type: integer
                  description: The number of hotels to return
              required:
                - city
                - country_code
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  hotels:
                    type: array
                    description: Unique Id to track the status of the send reminder Call
                    items:
                      type: object
                      properties:
                        hotelId:
                          type: string
                          description: Unique identifier for the hotel
                        name:
                          type: string
                          description: Name of the hotel
                        rating:
                          type: number
                          description: Hotel rating
                        address:
                          type: string
                          description: Hotel address
        "400":
          description: Bad request. One or more required fields are missing or invalid.