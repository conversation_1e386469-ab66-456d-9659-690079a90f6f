{"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["hotelIds", "check_in", "check_out"], "properties": {"hotelIds": {"type": "array", "description": "List of hotel IDs to check availability for", "items": {"type": "string"}, "example": ["lp656bbe43"]}, "occupancies": {"type": "array", "description": "Room occupancy details", "items": {"type": "object", "required": ["adults", "children"], "properties": {"adults": {"type": "integer", "minimum": 1, "description": "Number of adults (at least 1)", "example": 2}, "children": {"type": "array", "description": "Ages of children. Use empty array if none.", "items": {"type": "integer", "minimum": 0}, "example": [4]}}}}, "currency": {"type": "string", "description": "Currency code for pricing", "example": "INR"}, "guest_nationality": {"type": "string", "description": "Nationality of guest (ISO 2-letter code)", "example": "IN"}, "check_in": {"type": "string", "format": "date", "description": "Check-in date (YYYY-MM-DD)", "example": "2025-07-01"}, "check_out": {"type": "string", "format": "date", "description": "Check-out date (YYYY-MM-DD)", "example": "2025-07-02"}, "cityName": {"type": "string", "description": "City name", "example": "Puducherry"}, "countryCode": {"type": "string", "description": "Country code (ISO 2-letter)", "example": "IN"}, "zip": {"type": "string", "description": "ZIP/Postal code", "example": "605007"}}, "x-amzn-bedrock-parameterHints": {"hotelIds": {"type": "array"}, "occupancies": {"type": "array"}, "occupancies[].children": {"type": "array"}}}, "example": {"hotelIds": ["lp656bbe43"], "occupancies": [{"adults": 2, "children": [4]}], "currency": "INR", "guest_nationality": "IN", "check_in": "2025-07-01", "check_out": "2025-07-02", "cityName": "Puducherry", "countryCode": "IN", "zip": "605007"}}}}