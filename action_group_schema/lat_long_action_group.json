{"openapi": "3.0.0", "info": {"title": "Get Lat Long API", "version": "1.0.0", "description": "API to get latitude, longitude and zip code from a location name"}, "paths": {"/getLatLong": {"post": {"summary": "Get latitude, longitude and zip code from a location name", "description": "Gets the latitude, longitude and zip code from a location name", "operationId": "getLatLong", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["locationName"], "properties": {"locationName": {"type": "string", "description": "Name of the location", "example": "White town, Puducherry, India"}}}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"latitude": {"type": "string", "description": "Latitude of the location"}, "longitude": {"type": "string", "description": "Longitude of the location"}, "zip": {"type": "string", "description": "Zip code of the location"}}}}}}, "400": {"description": "Bad request. One or more required fields are missing or invalid."}, "500": {"description": "Server error"}}}}}}