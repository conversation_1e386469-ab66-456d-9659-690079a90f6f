#!/bin/bash

# <PERSON>ript to package and update Lambda functions for the Bedrock Agent

# Default values
LAMBDA_FUNCTION_NAME="hotel-availability-function"
LAMBDA_DIR="../lambda"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --lambda-name)
      LAMBDA_FUNCTION_NAME="$2"
      shift 2
      ;;
    --lambda-dir)
      LAMBDA_DIR="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --lambda-name NAME      Base name for Lambda functions (default: hotel-availability-function)"
      echo "  --lambda-dir DIR        Directory containing Lambda code (default: ../lambda)"
      echo "  --help                  Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Check if Lambda directory exists
if [ ! -d "$LAMBDA_DIR" ]; then
  echo "Error: Lambda directory '$LAMBDA_DIR' does not exist"
  exit 1
fi

# Check if required files exist
if [ ! -f "$LAMBDA_DIR/hotel_api.py" ]; then
  echo "Error: hotel_api.py not found in $LAMBDA_DIR"
  exit 1
fi

if [ ! -f "$LAMBDA_DIR/get_lat_long.py" ]; then
  echo "Error: get_lat_long.py not found in $LAMBDA_DIR"
  exit 1
fi

# Create temporary directory for packaging
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Package hotel_api Lambda function
echo "Packaging hotel_api.py..."
cp "$LAMBDA_DIR/hotel_api.py" "$TEMP_DIR/"
cd "$TEMP_DIR"
zip -r hotel-function.zip hotel_api.py
if [ $? -ne 0 ]; then
  echo "Error: Failed to create hotel-function.zip"
  rm -rf "$TEMP_DIR"
  exit 1
fi

# Package get_lat_long Lambda function
echo "Packaging get_lat_long.py..."
cp "$LAMBDA_DIR/get_lat_long.py" "$TEMP_DIR/"
zip -r latlong-function.zip get_lat_long.py
if [ $? -ne 0 ]; then
  echo "Error: Failed to create latlong-function.zip"
  rm -rf "$TEMP_DIR"
  exit 1
fi

# Update Lambda functions
echo "Updating hotel availability Lambda function..."
aws lambda update-function-code \
  --function-name "$LAMBDA_FUNCTION_NAME" \
  --zip-file fileb://hotel-function.zip

if [ $? -ne 0 ]; then
  echo "Error: Failed to update hotel availability Lambda function"
  rm -rf "$TEMP_DIR"
  exit 1
fi

echo "Updating lat/long Lambda function..."
aws lambda update-function-code \
  --function-name "$LAMBDA_FUNCTION_NAME-latlong" \
  --zip-file fileb://latlong-function.zip

if [ $? -ne 0 ]; then
  echo "Error: Failed to update lat/long Lambda function"
  rm -rf "$TEMP_DIR"
  exit 1
fi

# Clean up
rm -rf "$TEMP_DIR"
echo "Lambda functions updated successfully!"
