#!/bin/bash

# Tripzy Agent CloudFormation Deployment Script
# Reads environment variables and instruction files for deployment

# Determine script location and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"  # Change to project root directory

# Default environment
ENVIRONMENT=$1

# Check if environment is provided
if [ -z "$ENVIRONMENT" ]; then
  echo "Error: Environment value is missing."
  echo "Usage: ./devOps/deploy.sh <environment_name>"
  echo "   or: cd devOps && ./deploy.sh <environment_name>"
  exit 1
fi

# Default environment variables
PREFIX="tripzy"
STACK_NAME="tripzy-ai-agent-resources"
TEMPLATE_FILE="$SCRIPT_DIR/bedrock-agent.yaml"
REGION=${AWS_DEFAULT_REGION:-us-east-1}
ARTIFACTS_S3_BUCKET="${PREFIX}-ai-agent-${ENVIRONMENT}-artifacts"

# File paths
SRC_DIR="$PROJECT_ROOT/src"
PROMPT_DIR="$SRC_DIR/agent_prompts"
SCHEMA_DIR="$SRC_DIR/action_group_schema"

# Instruction files
TRIPZY_MASTER_AGENT_INSTRUCTION_FILE="$PROMPT_DIR/tripzy_master_agent.txt"
HOTEL_AVAILABILITY_INSTRUCTION_FILE="$PROMPT_DIR/hotel_availability_agent.txt"
RESTAURANT_AVAILABILITY_INSTRUCTION_FILE="$PROMPT_DIR/restaurant_availability_agent.txt"

# Schema files
GET_CURRENT_TIME_SCHEMA_FILE="$SCHEMA_DIR/getCurrentTime.json"
HOTEL_AVAILABILITY_SCHEMA_FILE="$SCHEMA_DIR/getHotelAvailability.json"
RESTAURANT_AVAILABILITY_SCHEMA_FILE="$SCHEMA_DIR/getRestaurantAvailability.json"

# Function to load environment variables from env file
load_env_file() {
  local env_file="$PROJECT_ROOT/env/$1.env"
  echo "Loading environment variables from $1.env"

  if [ ! -f "$env_file" ]; then
    echo "Warning: Environment file $env_file not found."
    return 1
  fi

  # Read variables from env file
  while IFS= read -r line || [[ -n "$line" ]]; do
    # Skip empty lines and comments
    [[ -z "$line" || "$line" =~ ^# ]] && continue

    # Extract variable name and value
    if [[ "$line" =~ ^([A-Za-z0-9_]+)=\"?([^\"]*)\"?$ ]]; then
      local name="${BASH_REMATCH[1]}"
      local value="${BASH_REMATCH[2]}"

      # Set API keys or export other variables
      case "$name" in
        LITEAPI_KEY|GEOAPIFY_KEY) eval "$name=\"$value\"" ;;
        *) export "$name"="$value" ;;
      esac
    fi
  done < "$env_file"

  return 0
}

# Function to upload file to S3 and return the S3 URL
upload_to_s3() {
  local file_path="$1"
  local s3_key="$2"
  local s3_url="s3://$ARTIFACTS_S3_BUCKET/$s3_key"

  echo "Uploading $file_path to $s3_url" >&2
  aws s3 cp "$file_path" "$s3_url" >&2

  if [ $? -eq 0 ]; then
    echo "https://$ARTIFACTS_S3_BUCKET.s3.$REGION.amazonaws.com/$s3_key"
  else
    echo "Error: Failed to upload $file_path to S3" >&2
    exit 1
  fi
}

# Default flags
NO_FAIL_ON_EMPTY_CHANGESET=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --env|--environment) ENVIRONMENT="$2"; shift 2 ;;
    --stack-name) STACK_NAME="$2"; shift 2 ;;
    --template-file) TEMPLATE_FILE="$2"; shift 2 ;;
    --prefix) PREFIX="$2"; shift 2 ;;
    --liteapi-key) LITEAPI_KEY="$2"; shift 2 ;;
    --geoapify-key) GEOAPIFY_KEY="$2"; shift 2 ;;
    --no-fail-on-empty-changeset) NO_FAIL_ON_EMPTY_CHANGESET=true; shift ;;
    --help)
      echo "Usage: $0 [environment] [options]"
      echo "       Can be run from any directory"
      echo ""
      echo "Examples:"
      echo "  ./devOps/deploy.sh dev                 # From project root"
      echo "  cd devOps && ./deploy.sh prod          # From devOps directory"
      echo "  /path/to/travel-agent-backend/devOps/deploy.sh dev  # From anywhere"
      echo ""
      echo "Options:"
      echo "  --env, --environment ENV  Environment name"
      echo "  --stack-name NAME         CloudFormation stack name"
      echo "  --template-file PATH      Path to CloudFormation template file"
      echo "  --prefix NAME             Prefix for resource names"
      echo "  --liteapi-key KEY         LiteAPI key (overrides env file)"
      echo "  --geoapify-key KEY        GeoApiFy key (overrides env file)"
      echo "  --help                    Show this help message"
      echo ""
      echo "Environment files are loaded from env/ENV.env"
      exit 0
      ;;
    *) shift ;;
  esac
done

# Load environment variables
echo "Using environment: $ENVIRONMENT"
load_env_file "$ENVIRONMENT"
if [ $? -ne 0 ]; then
  echo "Warning: Using default or command-line provided values for API keys."
else
  echo "Successfully loaded environment variables for $ENVIRONMENT environment."
fi

# Check if required files exist
check_file() {
  if [ ! -f "$1" ]; then
    echo "Error: File not found: $1"
    exit 1
  fi
}

check_file "$TEMPLATE_FILE"
check_file "$TRIPZY_MASTER_AGENT_INSTRUCTION_FILE"
check_file "$HOTEL_AVAILABILITY_INSTRUCTION_FILE"
check_file "$RESTAURANT_AVAILABILITY_INSTRUCTION_FILE"
check_file "$GET_CURRENT_TIME_SCHEMA_FILE"
check_file "$HOTEL_AVAILABILITY_SCHEMA_FILE"
check_file "$RESTAURANT_AVAILABILITY_SCHEMA_FILE"

# Create S3 bucket if it doesn't exist
echo "Checking S3 bucket: $ARTIFACTS_S3_BUCKET"
if ! aws s3api head-bucket --bucket "$ARTIFACTS_S3_BUCKET" 2>/dev/null; then
  echo "Creating S3 bucket: $ARTIFACTS_S3_BUCKET"
  if [ "$REGION" = "us-east-1" ]; then
    aws s3api create-bucket --bucket "$ARTIFACTS_S3_BUCKET"
  else
    aws s3api create-bucket --bucket "$ARTIFACTS_S3_BUCKET" --region "$REGION" --create-bucket-configuration LocationConstraint="$REGION"
  fi

  if [ $? -ne 0 ]; then
    echo "Error: Failed to create S3 bucket: $ARTIFACTS_S3_BUCKET"
    exit 1
  fi
fi

# Read instruction files and check size limits
echo "Reading instruction files..."

# Function to check file size and read content
read_instruction_file() {
  local file_path="$1"
  local max_size=4000  # Leave some buffer under 4096 limit

  local file_size=$(wc -c < "$file_path")
  if [ "$file_size" -gt "$max_size" ]; then
    echo "Error: File $file_path is too large ($file_size bytes). CloudFormation parameter limit is 4096 characters." >&2
    echo "Consider shortening the instruction content." >&2
    exit 1
  fi

  cat "$file_path" | jq -Rs .
}

# Read instruction files
TRIPZY__MASTER_INSTRUCTION_JSON=$(read_instruction_file "$TRIPZY_MASTER_AGENT_INSTRUCTION_FILE")
HOTEL_AVAILABILITY_INSTRUCTION_JSON=$(read_instruction_file "$HOTEL_AVAILABILITY_INSTRUCTION_FILE")
RESTAURANT_AVAILABILITY_INSTRUCTION_JSON=$(read_instruction_file "$RESTAURANT_AVAILABILITY_INSTRUCTION_FILE")

echo "All instruction files are within size limits."

# Upload schema files to S3
echo "Uploading schema files to S3..."

# Function to upload file to S3 and return the S3 key
upload_file_to_s3() {
  local file_path="$1"
  local s3_key="$2"
  local s3_url="s3://$ARTIFACTS_S3_BUCKET/$s3_key"

  echo "Uploading $file_path to $s3_url" >&2
  aws s3 cp "$file_path" "$s3_url" >&2

  if [ $? -eq 0 ]; then
    echo "$s3_key"  # Return just the S3 key
  else
    echo "Error: Failed to upload $file_path to S3" >&2
    exit 1
  fi
}

# Upload schema files only
GET_CURRENT_TIME_SCHEMA_S3_KEY=$(upload_file_to_s3 "$GET_CURRENT_TIME_SCHEMA_FILE" "schemas/getCurrentTime.json")
HOTEL_AVAILABILITY_SCHEMA_S3_KEY=$(upload_file_to_s3 "$HOTEL_AVAILABILITY_SCHEMA_FILE" "schemas/getHotelAvailability.json")
RESTAURANT_AVAILABILITY_SCHEMA_S3_KEY=$(upload_file_to_s3 "$RESTAURANT_AVAILABILITY_SCHEMA_FILE" "schemas/getRestaurantAvailability.json")

# Debug: Print the processing results
echo "Processing results:"
echo "  Instructions: Read from files and passed as parameters"
echo "  Schema S3 keys:"
echo "    Get Current Time Schema: $GET_CURRENT_TIME_SCHEMA_S3_KEY"
echo "    Hotel Availability Schema: $HOTEL_AVAILABILITY_SCHEMA_S3_KEY"
echo "    Restaurant Availability Schema: $RESTAURANT_AVAILABILITY_SCHEMA_S3_KEY"

# Confirm deployment
echo
echo "Deploying CloudFormation stack for environment: $ENVIRONMENT"
echo "  Region: $REGION"
echo "  Stack Name: $STACK_NAME"
echo "  API Keys: LiteAPI (${LITEAPI_KEY:0:5}...), GeoApiFy (${GEOAPIFY_KEY:0:5}...)"
echo ""
read -p "Continue with deployment? (y/n): " CONFIRM
if [[ $CONFIRM != "y" && $CONFIRM != "Y" ]]; then
  echo "Deployment cancelled"
  exit 0
fi

# Check if SAM CLI is installed
if ! command -v sam &> /dev/null; then
  echo "Error: AWS SAM CLI is not installed."
  echo "Please install AWS SAM CLI to deploy this stack:"
  echo "https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
  exit 1
fi

# Build the parameter overrides in the correct format for SAM CLI
# Use array to properly handle instruction content and schema S3 keys
PARAMETER_OVERRIDES=(
  "ParameterKey=Prefix,ParameterValue=$PREFIX"
  "ParameterKey=Environment,ParameterValue=$ENVIRONMENT"
  "ParameterKey=ArtifactsS3Bucket,ParameterValue=$ARTIFACTS_S3_BUCKET"
  "ParameterKey=TripzyMasterAgentInstruction,ParameterValue=$TRIPZY__MASTER_INSTRUCTION_JSON"
  "ParameterKey=HotelAvailabilityAgentInstruction,ParameterValue=$HOTEL_AVAILABILITY_INSTRUCTION_JSON"
  "ParameterKey=RestaurantAvailabilityAgentInstruction,ParameterValue=$RESTAURANT_AVAILABILITY_INSTRUCTION_JSON"
  "ParameterKey=GetCurrentTimeActionGroupSchemaS3Key,ParameterValue=$GET_CURRENT_TIME_SCHEMA_S3_KEY"
  "ParameterKey=HotelAvailabilityActionGroupSchemaS3Key,ParameterValue=$HOTEL_AVAILABILITY_SCHEMA_S3_KEY"
  "ParameterKey=RestaurantAvailabilityActionGroupSchemaS3Key,ParameterValue=$RESTAURANT_AVAILABILITY_SCHEMA_S3_KEY"
  "ParameterKey=LiteApiKey,ParameterValue=$LITEAPI_KEY"
  "ParameterKey=GeoApiFyApiKey,ParameterValue=$GEOAPIFY_KEY"
)

sam deploy \
  --template-file "$TEMPLATE_FILE" \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --s3-bucket "$ARTIFACTS_S3_BUCKET" \
  --s3-prefix artifacts \
  --parameter-overrides "${PARAMETER_OVERRIDES[@]}" \
  --capabilities CAPABILITY_NAMED_IAM \
  --no-fail-on-empty-changeset