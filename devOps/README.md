# Deploying the Travel Agent with Bedrock

This directory contains scripts and templates to deploy the Travel Agent with Bedrock.

## Prerequisites

1. AWS CLI installed and configured with appropriate permissions
2. API keys for external services:
   - LiteAPI key for hotel availability
   - GeoApiFy key for restaurant availability

## Deployment Steps

1. Navigate to the `devOps` directory
2. Run the `deploy.sh` script with the desired environment name
   ```bash
   ./deploy.sh dev
   ```
3. Follow the prompts to confirm deployment
4. The script will output the CloudFormation stack name and other relevant information