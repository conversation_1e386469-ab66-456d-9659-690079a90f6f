AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'CloudFormation template to create a Bedrock Agent for Hotel Availability'

Parameters:
  Prefix:
    Type: String
    Default: 'tripzy'
    Description: Prefix for all resource names
  Environment:
    Type: String
    Default: 'dev'
    Description: Environment name for resource tagging
  LiteApiKey:
    Type: String
    Default: ''
    Description: LiteAPI Key for hotel availability (optional)
    NoEcho: true
  GeoApiFyApiKey:
    Type: String
    Default: ''
    Description: GeoApiFy Key for restaurant availability (optional)
    NoEcho: true
  PythonRuntime:
    Type: String
    Default: 'python3.13'
    Description: Python runtime for Lambda functions
  AgentDescription:
    Type: String
    Default: 'Tripzy Travel Agent for Puducherry tourism'
    Description: Description for the Bedrock Agent
  FoundationModel:
    Type: String
    Default: 'anthropic.claude-3-5-sonnet-20240620-v1:0'
    Description: Foundation model for the Bedrock Agent
  ArtifactsS3Bucket:
    Type: String
    Description: S3 bucket containing the schema files
  TripzyMasterAgentInstruction:
    Type: String
    Description: Instruction content for the Tripzy Agent
    NoEcho: true
  HotelAvailabilityAgentInstruction:
    Type: String
    Description: Instruction content for the Hotel Availability Agent
    NoEcho: true
  RestaurantAvailabilityAgentInstruction:
    Type: String
    Description: Instruction content for the Restaurant Availability Agent
    NoEcho: true
  GetCurrentTimeActionGroupSchemaS3Key:
    Type: String
    Description: S3 key for the Get Current Time Action Group schema file
  HotelAvailabilityActionGroupSchemaS3Key:
    Type: String
    Description: S3 key for the Hotel Availability Action Group schema file
  RestaurantAvailabilityActionGroupSchemaS3Key:
    Type: String
    Description: S3 key for the Restaurant Availability Action Group schema file


Resources:
  # IAM Role for the Bedrock Agent
  BedrockAgentRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${Prefix}-${Environment}-bedrock-agent-role"
      Path: /service-role/
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: bedrock.amazonaws.com
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                "aws:SourceAccount": !Sub "${AWS::AccountId}"
      Policies:
        - PolicyName: !Sub "${Prefix}-${Environment}-bedrock-agent-policy"
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - "bedrock:InvokeModel"
                  - "bedrock:InvokeModelWithResponseStream"
                Resource: !Sub "arn:aws:bedrock:${AWS::Region}::foundation-model/${FoundationModel}"
              - Effect: Allow
                Action:
                  - "bedrock:GetAgentAlias"
                  - "bedrock:InvokeAgent"
                Resource: !Sub  "arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent-alias/*"
              - Effect: Allow
                Action:
                  - "s3:GetObject"
                  - "s3:GetObjectVersion"
                Resource: !Sub "arn:aws:s3:::${ArtifactsS3Bucket}/*"
              - Effect: Allow
                Action:
                  - "s3:ListBucket"
                Resource: !Sub "arn:aws:s3:::${ArtifactsS3Bucket}"
      Tags:
        - Key: Project
          Value: !Ref Prefix
        - Key: Environment
          Value: !Ref Environment

  # IAM Role for Lambda Functions
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${Prefix}-${Environment}-lambda-execution-role
      Path: /service-role/
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Tags:
        - Key: Project
          Value: !Ref Prefix
        - Key: Environment
          Value: !Ref Environment

  # Lambda Function for Getting Current Time
  GetCurrentTimeFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Prefix}-${Environment}-get-current-time"
      CodeUri: ../src/action_groups/
      Handler: getCurrentTime.lambda_handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Runtime: !Ref PythonRuntime
      Timeout: 30
      MemorySize: 256
      Handler: ../src/action_groups/getCurrentTime.lambda_handler
  GetCurrentTimeLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/lambda/${Prefix}-${Environment}-get-current-time
      RetentionInDays: 14
  GetCurrentTimeAgentTriggerPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !GetAtt GetCurrentTimeFunction.Arn
      Action: lambda:InvokeFunction
      Principal: bedrock.amazonaws.com
      SourceArn: !Sub "arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent/*"

  # Lambda Function for Hotel Availability
  HotelAvailabilityFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Prefix}-${Environment}-get-hotel-availability"
      CodeUri: ../src/action_groups/
      Handler: getHotelAvailability.lambda_handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Runtime: !Ref PythonRuntime
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          LITEAPI_KEY: !Ref LiteApiKey
      Handler: ../src/action_groups/getHotelAvailability.lambda_handler
  HotelAvailabilityLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/lambda/${Prefix}-${Environment}-get-hotel-availability
      RetentionInDays: 14
  HotelAvailabilityAgentTriggerPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !GetAtt HotelAvailabilityFunction.Arn
      Action: lambda:InvokeFunction
      Principal: bedrock.amazonaws.com
      SourceArn: !Sub "arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent/*"

  # Lambda Function for Lat/Long Lookup
  RestaurantAvailabilityFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Prefix}-${Environment}-get-restaurant-availability"
      CodeUri: ../src/action_groups/
      Handler: getRestaurantAvailability.lambda_handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Runtime: !Ref PythonRuntime
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          GEOAPIFY_KEY: !Ref GeoApiFyApiKey
  RestaurantAvailabilityLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/lambda/${Prefix}-${Environment}-get-restaurant-availability
      RetentionInDays: 14
  RestaurantAvailabilityAgentTriggerPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !GetAtt RestaurantAvailabilityFunction.Arn
      Action: lambda:InvokeFunction
      Principal: bedrock.amazonaws.com
      SourceArn: !Sub "arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent/*"

  # Bedrock Agents
  TripzyMasterAgent:
    Type: AWS::Bedrock::Agent
    Properties:
      AgentName: !Sub "${Prefix}-${Environment}-master-agent"
      AgentResourceRoleArn: !GetAtt BedrockAgentRole.Arn
      Description: !Ref AgentDescription
      AutoPrepare: true
      IdleSessionTTLInSeconds: 1800
      FoundationModel: !Ref FoundationModel
      Instruction: !Ref TripzyMasterAgentInstruction
      ActionGroups:
        - ActionGroupName: GetCurrentTime
          ActionGroupExecutor:
            Lambda: !GetAtt GetCurrentTimeFunction.Arn
          Description: 'This is an action group to get the current date and time'
          ApiSchema:
            S3:
              S3BucketName: !Ref ArtifactsS3Bucket
              S3ObjectKey: !Ref GetCurrentTimeActionGroupSchemaS3Key
      Tags:
        Project: !Ref Prefix
        Environment: !Ref Environment
  HotelAvailabilityAgent:
    Type: AWS::Bedrock::Agent
    Properties:
      AgentName: !Sub "${Prefix}-${Environment}-hotel-availability-agent"
      AgentResourceRoleArn: !GetAtt BedrockAgentRole.Arn
      Description: !Ref AgentDescription
      AutoPrepare: true
      IdleSessionTTLInSeconds: 1800
      FoundationModel: !Ref FoundationModel
      Instruction: !Ref HotelAvailabilityAgentInstruction
      ActionGroups:
        - ActionGroupName: GetCurrentTime
          ActionGroupExecutor:
            Lambda: !GetAtt GetCurrentTimeFunction.Arn
          Description: 'This is an action group to get the current date and time'
          ApiSchema:
            S3:
              S3BucketName: !Ref ArtifactsS3Bucket
              S3ObjectKey: !Ref GetCurrentTimeActionGroupSchemaS3Key
        - ActionGroupName: HotelAvailability
          ActionGroupExecutor:
            Lambda: !GetAtt HotelAvailabilityFunction.Arn
          Description: 'This is an action group to check hotel availability'
          ApiSchema:
            S3:
              S3BucketName: !Ref ArtifactsS3Bucket
              S3ObjectKey: !Ref HotelAvailabilityActionGroupSchemaS3Key
      Tags:
        Project: !Ref Prefix
        Environment: !Ref Environment
  RestaurantAvailabilityAgent:
    Type: AWS::Bedrock::Agent
    Properties:
      AgentName: !Sub "${Prefix}-${Environment}-restaurant-availability-agent"
      AgentResourceRoleArn: !GetAtt BedrockAgentRole.Arn
      Description: !Ref AgentDescription
      AutoPrepare: true
      IdleSessionTTLInSeconds: 1800
      FoundationModel: !Ref FoundationModel
      Instruction: !Ref RestaurantAvailabilityAgentInstruction
      ActionGroups:
        - ActionGroupName: GetCurrentTime
          ActionGroupExecutor:
            Lambda: !GetAtt GetCurrentTimeFunction.Arn
          Description: 'This is an action group to get the current date and time'
          ApiSchema:
            S3:
              S3BucketName: !Ref ArtifactsS3Bucket
              S3ObjectKey: !Ref GetCurrentTimeActionGroupSchemaS3Key
        - ActionGroupName: RestaurantAvailability
          ActionGroupExecutor:
            Lambda: !GetAtt RestaurantAvailabilityFunction.Arn
          Description: 'Action group to check restaurant availability'
          ApiSchema:
            S3:
              S3BucketName: !Ref ArtifactsS3Bucket
              S3ObjectKey: !Ref RestaurantAvailabilityActionGroupSchemaS3Key
      Tags:
        Project: !Ref Prefix
        Environment: !Ref Environment

Outputs:
  TripzyMasterAgentId:
    Description: ID of the created Bedrock Agent
    Value: !Ref TripzyMasterAgent
  HotelAvailabilityAgentId:
    Description: ID of the created Bedrock Agent
    Value: !Ref HotelAvailabilityAgent
  RestaurantAvailabilityAgentId:
    Description: ID of the created Bedrock Agent
    Value: !Ref RestaurantAvailabilityAgent
  HotelAvailabilityFunctionArn:
    Description: ARN of the Hotel Availability Lambda function
    Value: !GetAtt HotelAvailabilityFunction.Arn