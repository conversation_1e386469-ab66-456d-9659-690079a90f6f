import json
import datetime

def lambda_handler(event, context):
    """
    Lambda function to get the current time in IST timezone.
    
    Returns:
    - Current time information in UTC and IST
    """
    # Get current UTC time
    current_utc = datetime.datetime.utcnow()
    utc_time_str = current_utc.strftime('%Y-%m-%d %H:%M:%S UTC')
    
    # Convert to IST (Indian Standard Time) - UTC+5:30
    ist_offset = datetime.timedelta(hours=5, minutes=30)
    ist_time = current_utc + ist_offset
    ist_time_str = ist_time.strftime('%Y-%m-%d %H:%M:%S') + " IST"
    
    response = {
        "utc_time": utc_time_str,
        "ist_time": ist_time_str,
        "timestamp": int(current_utc.timestamp())
    }

    actionResponse = {
            "actionGroup": event.get("actionGroup"),
            "apiPath": event.get("apiPath"),
            "httpMethod": event.gt("httpMethod"),
            "httpStatusCode": 200,
            "responseBody": {
                "application/json": {
                    "body": json.dumps(response)
                }
            }
        }
    finalResponse = { "response": actionResponse, "messageVersion": event.get("messageVersion") }
    return finalResponse