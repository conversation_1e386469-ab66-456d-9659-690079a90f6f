import json
import urllib.request
import urllib.parse
import urllib.error
import os

def lambda_handler(event, context):
    print("Event received:", json.dumps(event))

    try:
        request_body = event.get('requestBody', {}).get('content', {}).get('application/json', {}).get('properties', [])
        params = {prop.get('name'): parse_value(prop.get('value')) for prop in request_body}

        api_path = event.get("apiPath")
        api_key = os.environ.get('LITEAPI_KEY', 'sand_cb32682e-bc9a-47da-a66c-2e5816da9c1f')
        base_url = f"https://api.liteapi.travel/v3.0{api_path}"

        if api_path == "/data/hotels":
            # Filters for listing hotels
            query_params = {
                'countryCode': params.get('country_code', 'IN'),
                'cityName': params.get('city', 'Puducherry'),
                'zip': params.get('zip', '605007'),
                'offset': 0,
                'limit': int(params.get('limit', 5))
            }

            # if params.get('latitude') and params.get('longitude'):
            #     query_params['latitude'] = params['latitude']
            #     query_params['longitude'] = params['longitude']
            #     query_params['distance'] = params.get('distance', 10000)

            url = f"{base_url}?{urllib.parse.urlencode(query_params)}"
            req = urllib.request.Request(url)

        elif api_path == "/hotels/rates":
          # Filters for availability & pricing

          payload = {
          "hotelIds": params.get("hotelIds"),
          "occupancies": [
              {
                  "adults": eval(params.get("adults")),
                  "children": [eval(params.get("children"))]
              }
              
          ],
          "currency": params.get("currency", "INR"),
          "guestNationality": params.get("guest_nationality", "IN"),
          "checkin": params.get("check_in"),
          "checkout": params.get("check_out"),
          "cityName": params.get("cityName", "Puducherry"),
          "countryCode": params.get("countryCode", "IN"),
          "zip": params.get("zip", "605007")
      }
          payload = {k: v for k, v in payload.items() if v is not None}
          data = json.dumps(payload).encode("utf-8")          
          req = urllib.request.Request(base_url, data=data, method="POST")


        elif api_path == "/data/reviews":
          # Reviews and sentiment analysis
          query_params = {
              "hotelId": params.get("hotelId"),
              "getSentiment": "false"
          }
          url = f"{base_url}?{urllib.parse.urlencode(query_params)}"
          req = urllib.request.Request(url)

        else:
          return format_error_response(event, 404, f"Unsupported API path: {api_path}")

        req.add_header("Content-Type", "application/json")
        req.add_header('Accept', 'application/json')
        req.add_header('X-API-Key', api_key)

        with urllib.request.urlopen(req) as response:
            response_data = response.read().decode('utf-8')
            data = json.loads(response_data)
            hotel_details = []

            if api_path == "/data/hotels":
                for hotels in data.get('data', []):
                  hotel_info = {
                      'id': hotels.get('id'),
                      'name': hotels.get('name'),
                      'city': hotels.get('city'),
                      'address': hotels.get('address'),
                      'zip': hotels.get('zip'),
                      'stars': hotels.get('stars'),
                      'currency': hotels.get('currency'),
                      'main_photo': hotels.get('main_photo'),
                      'latitude': hotels.get('latitude'),
                      'longitude': hotels.get('longitude')
                  }
                  hotel_details.append(hotel_info)

            elif api_path == "/hotels/rates": 
              for hotels in data.get("data", []):
                hotel_id = hotels.get("hotelId")
                for room in hotels.get("roomTypes", []):
                    room_type_id = room.get("roomTypeId")
                    for rate in room.get("rates", []):
                        hotel_info = {
                            "hotelId": hotel_id,
                            "roomTypeId": room_type_id,
                            "rateId": rate.get("rateId"),
                            "name": rate.get("name"),
                            "boardName": rate.get("boardName"),
                            "maxOccupancy": rate.get("maxOccupancy"),
                            "adultCount": rate.get("adultCount"),
                            "childCount": rate.get("childCount"),
                            "retailRate": rate.get("retailRate"),
                            "cancellationPolicies": rate.get("cancellationPolicies"),
                            "remarks": rate.get("remarks"),
                        }
                        hotel_details.append(hotel_info)

            elif api_path == "/data/reviews":
              total = data.get("total")
              if total == 0:
                   hotel_info = {
                     "total": data.get("total")
                   }
                   hotel_details.append(hotel_info)
              else:
                for review in data.get("data", []):
                    hotel_info = {
                        "averageScore": review.get("averageScore"),
                        "type": review.get("type"),
                        "name": review.get("name"),
                        "date": review.get("date"),
                        "headline": review.get("headline"),
                        "language": review.get("language"),
                        "pros": review.get("pros"),
                        "cons": review.get("cons"),
                        "total": review.get("total")
                    }
                    hotel_details.append(hotel_info)

            action_response = {
                "actionGroup": event.get("actionGroup"),
                "apiPath": api_path,
                "httpMethod": event.get("httpMethod"),
                "httpStatusCode": 200,
                "responseBody": {
                    "application/json": {
                        "body": hotel_details
                    }
                }
            }

            print(json.dumps(hotel_details, indent=4))
            return {
                "messageVersion": event.get("messageVersion", "1.0"),
                "response": action_response
            }

    except urllib.error.HTTPError as e:
        print(f"Response: {e.read().decode('utf-8')}")
        return format_error_response(event, e.code, f"HTTP Error: {e.reason}")  
    except urllib.error.URLError as e:
        return format_error_response(event, 500, f"URL Error: {str(e.reason)}")
    except Exception as e:
        return format_error_response(event, 500, f"Error processing request: {str(e)}")

def parse_value(val):
    if isinstance(val, str):
        val = val.strip()
        if val.startswith('[') or val.startswith('{'):
            try:
                return json.loads(val)
            except json.JSONDecodeError:
                pass  # leave as string if it can't be parsed
    return val

def format_error_response(event, status_code, error_message):
    return {
        "messageVersion": event.get("messageVersion", "1.0"),
        "response": {
            "actionGroup": event.get("actionGroup"),
            "apiPath": event.get("apiPath"),
            "httpMethod": event.get("httpMethod"),
            "httpStatusCode": status_code,
            "responseBody": {
                "application/json": {
                    "body": json.dumps({"error": error_message})
                }
            }
        }
    }


# hotel_search_event = {
#   "messageVersion": "1.0",
#   "actionGroup": "HotelSearch",
#   "apiPath": "/data/hotels",
#   "httpMethod": "GET",
#   "requestBody": {
#     "content": {
#       "application/json": {
#         "properties": [
#           {
#             "name": "city",
#             "value": "Puducherry"
#           },
#           {
#             "name": "country_code",
#             "value": "IN"
#           },
#           {
#             "name": "limit",
#             "value": "20"
#           },
#           {
#             "name": "latitude",
#             "value": "11.9139"
#           },
#           {
#             "name": "longitude",
#             "value": "79.8145"
#           },
#           {
#             "name": "distance",
#             "value": "10000"
#           }
#         ]
#       }
#     }
#   }
# }

hotel_search_event = {
  "messageVersion": "1.0",
  "actionGroup": "HotelRates",
  "apiPath": "/hotels/rates",
  "httpMethod": "POST",
  "requestBody": {
    "content": {
      "application/json": {
        "properties": [
            {
                "name": "hotelIds",
                "value": [
                    "lp656bbe43"
                ]
            },
          {
            "name": "countryCode",
            "value": "IN"
          },
          {
            "name": "cityName",
            "value": "Puducherry"
          },
          {
            "name": "zip",
            "value": "605007"
          },
          {
            "name": "adults",
            "value": 2
          },
          {
            "name": "children",
            "value": 1
          },
          {
            "name": "currency",
            "value": "INR"
          },
          {
            "name": "guest_nationality",
            "value": "IN"
          },
          {
            "name": "check_in",
            "value": "2025-07-01"
          },
          {
            "name": "check_out",
            "value": "2025-07-02"
          }
        ]
      }
    }
  }
}


# hotel_search_event = {
#   "messageVersion": "1.0",
#   "actionGroup": "HotelReviews",
#   "apiPath": "/data/reviews",
#   "httpMethod": "GET",
#   "requestBody": {
#     "content": {
#       "application/json": {
#         "properties": [
#           {
#             "name": "hotelId",
#             "value": "lp655af390"
#           }
#         ]
#       }
#     }
#   }
# }


rate_event = {
    "parameters": [],
    "sessionId": "140857882741856",
    "agent": {
        "name": "hotel-availability-agent",
        "version": "DRAFT",
        "id": "P5QSVMX5QO",
        "alias": "TSTALIASID"
    },
    "actionGroup": "getHotelAvailability",
    "messageVersion": "1.0",
    "sessionAttributes": {},
    "promptSessionAttributes": {},
    "inputText": "Find hotel in Puducherry. Check-in: 23rd May 2025. Check-out: 25th May 2025. Guests: 2 adults, 2 children.",
    "httpMethod": "POST",
    "apiPath": "/hotels/rates",
    "requestBody": {
        "content": {
            "application/json": {
                "properties": [
                    {
                        "name": "check_in",
                        "type": "string",
                        "value": "2025-06-23"
                    },
                    {
                        "name": "children",
                        "type": "integer",
                        "value": "2"
                    },
                    {
                        "name": "guest_nationality",
                        "type": "string",
                        "value": "IN"
                    },
                    {
                        "name": "countryCode",
                        "type": "string",
                        "value": "IN"
                    },
                    {
                        "name": "currency",
                        "type": "string",
                        "value": "INR"
                    },
                    {
                        "name": "cityName",
                        "type": "string",
                        "value": "Puducherry"
                    },
                    {
                        "name": "hotelIds",
                        "type": "array",
                        # "value": ["lp656bbe43"]
                        "value": "[\"lp3bbdf\",\"lp4c99a\",\"lp4e6b1\",\"lp4f70a\",\"lp4f70b\"]"
                    },
                    {
                        "name": "zip",
                        "type": "string",
                        "value": "605001"
                        # "value": "605007"
                    },
                    {
                        "name": "check_out",
                        "type": "string",
                        "value": "2025-06-25"
                    },
                    {
                        "name": "adults",
                        "type": "integer",
                        "value": "2"
                    }
                ]
            }
        }
    }
}


rate_event = {
    "messageVersion": "1.0",
    "parameters": [],
    "inputText": "Find hotel in Puducherry. Check-in: 23rd May 2025. Check-out: 25th May 2025. Guests: 2 adults, 2 children.",
    "sessionAttributes": {},
    "promptSessionAttributes": {},
    "sessionId": "140857882741363",
    "agent": {
        "name": "hotel-availability-agent",
        "version": "DRAFT",
        "id": "P5QSVMX5QO",
        "alias": "TSTALIASID"
    },
    "actionGroup": "getHotelAvailability",
    "httpMethod": "POST",
    "apiPath": "/hotels/rates",
    "requestBody": {
        "content": {
            "application/json": {
                "properties": [
                    {
                        "name": "check_in",
                        "type": "string",
                        "value": "2025-05-23"
                    },
                    {
                        "name": "children",
                        "type": "integer",
                        "value": "2"
                    },
                    {
                        "name": "guest_nationality",
                        "type": "string",
                        "value": "IN"
                    },
                    {
                        "name": "countryCode",
                        "type": "string",
                        "value": "IN"
                    },
                    {
                        "name": "currency",
                        "type": "string",
                        "value": "INR"
                    },
                    {
                        "name": "cityName",
                        "type": "string",
                        "value": "Puducherry"
                    },
                    {
                        "name": "hotelIds",
                        "type": "array",
                        "value": "[\"lpa7de2\",\"lpfad88\",\"lp1d5d6b\"]"
                    },
                    {
                        "name": "zip",
                        "type": "string",
                        "value": "605007"
                    },
                    {
                        "name": "check_out",
                        "type": "string",
                        "value": "2025-05-25"
                    },
                    {
                        "name": "adults",
                        "type": "integer",
                        "value": "2"
                    }
                ]
            }
        }
    }
}
response = lambda_handler(rate_event, None)
print(json.dumps(response, indent=2))