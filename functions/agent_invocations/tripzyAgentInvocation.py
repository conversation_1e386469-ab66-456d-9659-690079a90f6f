import os
import json
import boto3

# Initialize the Bedrock agent runtime client
client = boto3.client('bedrock-agent-runtime')

# Agent details
agent_alias_id = os.getenv("AGENT_ALIAS_ID", "HQMKFHMAPZ")
agent_id = os.getenv("BEDROCK_AGENT_ID", "YEMOIK4LMT")
SUCCESS = "MSG_SUC"
BAD_REQUEST = "MSG_ERR_BAD_REQ"
NO_DATA = "MSG_ERR_DATA_NOT_PROVIDED"

RESPONSE_HEADERS = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST,GET,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
}

def lambda_handler(event, context):
    try:
        print("Event received:", json.dumps(event))

        # Extract and validate the payload
        payload = extract_payload(event)
        
        if "error" in payload:
            return generate_response(400, BAD_REQUEST, {'error': payload["error"]})

        # Construct input text with extracted details
        input_text = payload['inputText']
        
        print("Constructed input text:", input_text)

        # Invoke the Bedrock agent
        response = client.invoke_agent(
            agentAliasId=agent_alias_id,
            agentId=agent_id,
            enableTrace=True,
            endSession=False,
            inputText=input_text,
            sessionId=payload["sessionId"]
        )

        # Process Bedrock agent response
        final_output = extract_final_output(response)
        
        if final_output:
            return generate_response(200, SUCCESS, {'message': final_output})
        else:
            return generate_response(200, SUCCESS, {'message': "Oops! It looks like something went wrong. Let’s try that again and see if we can get things working smoothly!"})

    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return generate_response(500, BAD_REQUEST, {'message': "Oops! It looks like something went wrong. Let’s try that again and see if we can get things working smoothly!"})

def extract_payload(event):
    try:
        # Parse request body from the event
        body = json.loads(event.get('body', '{}'))
        
        # Extract input fields
        payload = {
            'inputText': body.get('inputText'),
            'sessionId': body.get('sessionId', 'default-session')
        }

        # Validate missing fields
        missing_fields = [key for key, value in payload.items() if value is None]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
        
        return payload

    except ValueError as ve:
        print(f"Validation error: {ve}")
        return {'error': str(ve)}
        
    except Exception as e:
        print("Error parsing payload:", str(e))
        return {"error": "Error processing input"}

def extract_final_output(response):
    """Extract final output from Bedrock agent response"""
    try:
        for event in response.get("completion", []):
            trace = event.get('trace', {}).get('trace', {})
            orchestration_trace = trace.get('orchestrationTrace', {})
            print("orchestrationTrace: ", orchestration_trace)
            observation = orchestration_trace.get('observation', {})
            final_response = observation.get('finalResponse', {})
            if 'text' in final_response:
                return final_response['text']
    except Exception as e:
        print(f"Error extracting final output: {e}")
    
    return None

def generate_response(status_code=200, message=SUCCESS, response=None):
    """Generate the response object"""
    if response is None:
        response = {}
    RESPONSE_HEADERS = {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST,GET,OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    }
    return {
        "statusCode": status_code,
        # "headers": RESPONSE_HEADERS,
        "body": json.dumps({
            "message": message,
            "response": response
        }),
    }
