import boto3
import json

def simple_invoke(prompt):
    client = boto3.client("bedrock-runtime", region_name="us-east-1")
    
    body = {
        "anthropic_version": "bedrock-2023-05-31",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 512,
        "temperature": 0.5
    }

    response = client.invoke_model(
        modelId="anthropic.claude-3-haiku-20240307-v1:0",  # Replace with your preferred model
        contentType="application/json",
        accept="application/json",
        body=json.dumps(body)
    )

    response_body = json.loads(response["body"].read())
    return response_body["content"][0]["text"]

# Example usage:
if __name__ == "__main__":
    prompt = "Give me 3 tips to stay productive when working remotely."
    result = simple_invoke(prompt)
    print("Model response:", result)
