{"pagination": {"ListClusters": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ClusterInfoList"}, "ListNodes": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "NodeInfoList"}, "ListConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Configurations"}, "ListClusterOperations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ClusterOperationInfoList"}, "ListConfigurationRevisions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Revisions"}, "ListKafkaVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "KafkaVersions"}, "ListScramSecrets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SecretArnList"}, "ListClustersV2": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ClusterInfoList"}, "ListVpcConnections": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "VpcConnections"}, "ListClientVpcConnections": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ClientVpcConnections"}, "ListClusterOperationsV2": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ClusterOperationInfoList"}, "ListReplicators": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Replicators"}}}